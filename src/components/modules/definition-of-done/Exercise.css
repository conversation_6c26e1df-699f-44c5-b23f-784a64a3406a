/* Definition of Done Module Styles */

.definition-of-done-module {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* DoD Creation Workshop Styles */
.dod-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0;
}

/* Scenario Card - shared across exercises */
.scenario-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scenario-title {
  font-size: 20px;
  font-weight: 700;
  color: #212529;
  margin: 0 0 12px 0;
}

.scenario-description {
  color: #6c757d;
  line-height: 1.5;
  margin: 0 0 12px 0;
  font-size: 15px;
}

.scenario-context {
  color: #495057;
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
  font-style: italic;
}

.dod-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 0 0 24px 0;
}

.dod-category {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  min-height: 250px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dod-category.drag-over {
  border-color: #007bff;
  background: #e3f2fd;
}

.dod-category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #495057;
}

.dod-category-icon {
  font-size: 1.2em;
}

.dod-category-description {
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
  margin: 0 0 16px 0;
}

.dod-criteria-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dod-criterion {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dod-criterion:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.dod-criterion.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.dod-criterion-text {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.dod-criterion-priority {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Available Criteria Section */
.available-criteria {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.available-criteria h3 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.criteria-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.remove-criterion {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s ease;
}

.remove-criterion:hover {
  background: #c82333;
}

/* Acceptance Criteria Styles */
.ac-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ac-story-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ac-story-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 8px;
}

.ac-story-description {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 16px;
}

.user-story, .story-context {
  margin: 16px 0;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.business-rules, .edge-cases {
  margin: 20px 0;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.business-rules h4, .edge-cases h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.business-rules ul, .edge-cases ul {
  margin: 0;
  padding-left: 20px;
}

.business-rules li, .edge-cases li {
  margin: 8px 0;
  line-height: 1.4;
  color: #6c757d;
  font-size: 14px;
}

.ac-format-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.ac-format-button {
  padding: 8px 16px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.ac-format-button.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.ac-format-button:hover:not(.active) {
  border-color: #007bff;
  color: #007bff;
}

.ac-editor {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.ac-given-when-then {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ac-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ac-section-label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.ac-section-input {
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
  min-height: 60px;
}

.ac-section-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Quality Gates Styles */
.quality-gates-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0;
}

/* Workflow Description */
.workflow-description {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-title {
  font-size: 20px;
  font-weight: 700;
  color: #212529;
  margin: 0 0 12px 0;
}

.workflow-description p {
  color: #6c757d;
  line-height: 1.5;
  margin: 0 0 12px 0;
  font-size: 15px;
}

.workflow-description p:last-child {
  margin-bottom: 0;
  font-style: italic;
  color: #495057;
  font-size: 14px;
}

.workflow-canvas {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 32px;
  min-height: 450px;
  position: relative;
  margin-bottom: 24px;
}

.workflow-stages {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  gap: 20px;
}

.workflow-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  flex: 1;
  padding: 20px 16px;
  border: 2px dashed transparent;
  border-radius: 12px;
  transition: all 0.2s ease;
  background: white;
  min-height: 300px;
}

.workflow-stage.drag-over {
  border-color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.stage-header {
  background: #007bff;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  min-width: 140px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.stage-description {
  text-align: center;
  color: #6c757d;
  font-size: 12px;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.stage-gates {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  min-height: 180px;
}

.quality-gate {
  background: white;
  border: 2px solid #28a745;
  border-radius: 8px;
  padding: 16px;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quality-gate:hover {
  border-color: #20c997;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.quality-gate.dragging {
  opacity: 0.5;
  transform: rotate(3deg);
}

.remove-gate {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s ease;
}

.remove-gate:hover {
  background: #c82333;
}

.gate-title {
  font-weight: 600;
  font-size: 14px;
  color: #212529;
  margin-bottom: 4px;
}

.gate-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
  margin: 8px 0;
}

.gate-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  justify-content: center;
}

.gate-type, .gate-effort, .gate-impact {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
  font-weight: 500;
}

.gate-type {
  background: #e3f2fd;
  color: #1565c0;
}

.gate-effort {
  background: #fff3e0;
  color: #ef6c00;
}

.gate-impact {
  background: #e8f5e8;
  color: #2e7d32;
}

/* Quality Tips Section */
.quality-tips {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.quality-tips h3 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.tips-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.tip-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.tip-title {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.tip-description {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
}

.gate-toolbox {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.gate-toolbox-title {
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 18px;
}

.available-gates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

/* Exercise Instructions */
.exercise-instructions {
  background: #e3f2fd;
  border: 1px solid #90caf9;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.exercise-instructions h4 {
  margin: 0 0 12px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
}

.exercise-instructions ol {
  margin: 12px 0;
  padding-left: 20px;
}

.exercise-instructions li {
  margin: 8px 0;
  line-height: 1.4;
}

.exercise-instructions strong {
  color: #1565c0;
  font-weight: 600;
}

.exercise-instructions em {
  color: #1976d2;
  font-style: normal;
  font-weight: 500;
}

/* Shared Exercise Styles */
.exercise-header {
  text-align: center;
  margin: 0 0 32px 0;
  padding: 0 20px;
}

.exercise-title {
  font-size: 28px;
  font-weight: 700;
  color: #212529;
  margin-bottom: 8px;
}

.exercise-description {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
}

.scenario-progress {
  display: inline-block;
  background: #e9ecef;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 20px;
  margin-top: 16px;
}

.exercise-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.progress-step.active {
  background: #007bff;
  color: white;
}

.progress-step.completed {
  background: #28a745;
  color: white;
}

.progress-step.pending {
  background: #e9ecef;
  color: #6c757d;
}

.exercise-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 32px 0 0 0;
  padding: 24px 0;
  border-top: 1px solid #e9ecef;
}

.action-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.action-button.primary {
  background: #007bff;
  color: white;
}

.action-button.primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.action-button.secondary {
  background: #6c757d;
  color: white;
}

.action-button.secondary:hover {
  background: #545b62;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.start-button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  margin-top: 20px;
}

.start-button:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* Loading and Error States */
.loading-screen, .error-screen {
  text-align: center;
  padding: 40px 20px;
}

.loading-screen p, .error-screen p {
  font-size: 16px;
  color: #6c757d;
  margin-bottom: 20px;
}

.error-screen button {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.error-screen button:hover {
  background: #c82333;
}

/* Start Screen */
.start-screen {
  text-align: center;
  padding: 40px 20px;
  max-width: 600px;
  margin: 0 auto;
}

.start-screen p {
  font-size: 16px;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 30px;
  white-space: pre-line;
}

/* Feedback and Results */
.exercise-feedback {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.feedback-title {
  font-weight: 600;
  color: #155724;
  margin-bottom: 12px;
}

.feedback-content {
  color: #155724;
  line-height: 1.5;
}

.exercise-results {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.result-metric {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .definition-of-done-module {
    padding: 16px;
  }

  .dod-categories {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .dod-category {
    min-height: 200px;
    padding: 16px;
  }

  .workflow-stages {
    flex-direction: column;
    gap: 20px;
  }

  .workflow-stage {
    min-height: 200px;
    padding: 16px;
  }

  .available-gates {
    grid-template-columns: 1fr;
  }

  .criteria-list {
    grid-template-columns: 1fr;
  }

  .tips-list {
    grid-template-columns: 1fr;
  }

  .exercise-actions {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .action-button {
    width: 100%;
    max-width: 300px;
  }

  .scenario-card, .ac-story-card, .workflow-description, .gate-toolbox {
    padding: 20px;
    margin-bottom: 20px;
  }

  .exercise-instructions {
    padding: 16px;
    margin: 16px 0;
  }
}
