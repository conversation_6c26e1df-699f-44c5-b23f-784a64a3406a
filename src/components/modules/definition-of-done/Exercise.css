/* Definition of Done Module Styles */

.definition-of-done-module {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* DoD Creation Workshop Styles */
.dod-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dod-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.dod-category {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  min-height: 200px;
  transition: all 0.2s ease;
}

.dod-category.drag-over {
  border-color: #007bff;
  background: #e3f2fd;
}

.dod-category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #495057;
}

.dod-category-icon {
  font-size: 1.2em;
}

.dod-criteria-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dod-criterion {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dod-criterion:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.dod-criterion.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.dod-criterion-text {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.dod-criterion-priority {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Acceptance Criteria Styles */
.ac-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ac-story-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.ac-story-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 8px;
}

.ac-story-description {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 16px;
}

.ac-format-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.ac-format-button {
  padding: 8px 16px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.ac-format-button.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.ac-format-button:hover:not(.active) {
  border-color: #007bff;
  color: #007bff;
}

.ac-editor {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.ac-given-when-then {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ac-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ac-section-label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.ac-section-input {
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
  min-height: 60px;
}

.ac-section-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Quality Gates Styles */
.quality-gates-workshop {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.workflow-canvas {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 24px;
  min-height: 400px;
  position: relative;
}

.workflow-stages {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
}

.workflow-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  flex: 1;
  padding: 0 12px;
}

.stage-header {
  background: #007bff;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  min-width: 120px;
}

.stage-gates {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  min-height: 200px;
}

.quality-gate {
  background: white;
  border: 2px solid #28a745;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quality-gate:hover {
  border-color: #20c997;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.quality-gate.dragging {
  opacity: 0.5;
  transform: rotate(3deg);
}

.gate-title {
  font-weight: 600;
  font-size: 14px;
  color: #212529;
  margin-bottom: 4px;
}

.gate-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

.gate-toolbox {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.gate-toolbox-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #495057;
}

.available-gates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

/* Exercise Instructions */
.exercise-instructions {
  background: #e3f2fd;
  border: 1px solid #90caf9;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.exercise-instructions h4 {
  margin: 0 0 12px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
}

.exercise-instructions ol {
  margin: 12px 0;
  padding-left: 20px;
}

.exercise-instructions li {
  margin: 8px 0;
  line-height: 1.4;
}

.exercise-instructions strong {
  color: #1565c0;
  font-weight: 600;
}

.exercise-instructions em {
  color: #1976d2;
  font-style: normal;
  font-weight: 500;
}

/* Shared Exercise Styles */
.exercise-header {
  text-align: center;
  margin-bottom: 32px;
}

.exercise-title {
  font-size: 28px;
  font-weight: 700;
  color: #212529;
  margin-bottom: 8px;
}

.exercise-description {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
}

.exercise-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.progress-step.active {
  background: #007bff;
  color: white;
}

.progress-step.completed {
  background: #28a745;
  color: white;
}

.progress-step.pending {
  background: #e9ecef;
  color: #6c757d;
}

.exercise-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.action-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.action-button.primary {
  background: #007bff;
  color: white;
}

.action-button.primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.action-button.secondary {
  background: #6c757d;
  color: white;
}

.action-button.secondary:hover {
  background: #545b62;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Feedback and Results */
.exercise-feedback {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.feedback-title {
  font-weight: 600;
  color: #155724;
  margin-bottom: 12px;
}

.feedback-content {
  color: #155724;
  line-height: 1.5;
}

.exercise-results {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 24px;
  margin: 20px 0;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.result-metric {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dod-categories {
    grid-template-columns: 1fr;
  }
  
  .workflow-stages {
    flex-direction: column;
    gap: 24px;
  }
  
  .available-gates {
    grid-template-columns: 1fr;
  }
  
  .exercise-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-button {
    width: 100%;
    max-width: 300px;
  }
}
