{"metadata": {"moduleId": "definition-of-done", "version": "1.0.0", "lastUpdated": "2025-01-15", "description": "Definition of Done & Quality Gates Module Configuration"}, "module": {"title": "Definition of Done & Quality Gates", "description": "Establish clear quality standards and implement effective quality gates throughout development", "learningObjectives": ["Establish clear, measurable Definition of Done criteria", "Write effective, testable acceptance criteria", "Implement quality gates throughout development process", "Align team standards and quality expectations", "Understand the relationship between DoD and acceptance criteria"], "exercises": [{"id": 1, "title": "DoD Creation Workshop", "description": "Build comprehensive Definition of Done criteria for different types of work", "details": "Practice creating Definition of Done criteria that are clear, measurable, and appropriate for different types of development work. Learn to categorize and prioritize quality standards.", "type": "dod_creation", "dataFile": "dod-scenarios.json", "config": {"allowRetry": true, "showHints": true, "categories": ["technical", "functional", "process", "documentation"], "maxCriteriaPerCategory": 8}, "ui": {"startScreen": {"instructions": "You'll work through different development scenarios and build comprehensive Definition of Done criteria. Arrange quality standards into appropriate categories and prioritize them based on importance and feasibility.", "buttonText": "Start DoD Workshop"}}}, {"id": 2, "title": "Acceptance Criteria Mastery", "description": "Write clear, testable acceptance criteria using Given/When/Then format", "details": "Master the art of writing acceptance criteria that prevent ambiguity and reduce rework. Practice using Given/When/Then format and learn to identify missing scenarios.", "type": "acceptance_criteria", "dataFile": "ac-scenarios.json", "config": {"allowRetry": true, "showExamples": true, "formatTypes": ["given-when-then", "checklist", "scenario-based"], "qualityChecks": true}, "ui": {"startScreen": {"instructions": "You'll be presented with user stories that need acceptance criteria. Practice writing clear, testable criteria using different formats and learn to identify edge cases and missing scenarios.", "buttonText": "Start AC Workshop"}}}, {"id": 3, "title": "Quality Gates Assessment", "description": "Design quality checkpoints for development workflow", "details": "Learn to implement systematic quality gates throughout the development process. Practice identifying where quality checks should occur and what criteria should be evaluated at each gate.", "type": "quality_gates", "dataFile": "quality-gate-scenarios.json", "config": {"allowRetry": true, "showProcessFlow": true, "gateTypes": ["code-review", "testing", "deployment", "documentation"], "maxGatesPerWorkflow": 6}, "ui": {"startScreen": {"instructions": "You'll design quality gate processes for different development workflows. Place quality checkpoints at appropriate stages and define criteria for each gate to prevent defects from progressing.", "buttonText": "Start Quality Gates"}}}]}}