{"metadata": {"version": "2.0.0", "lastUpdated": "2025-01-14", "description": "Agile Learning Platform Configuration"}, "platform": {"name": "Agile Mastery <PERSON>", "subtitle": "Master agile practices through interactive learning", "modules": [{"id": "story-points", "title": "Story Point Estimation Mastery", "description": "Master the art of relative sizing in agile development", "icon": "🎯", "difficulty": "<PERSON><PERSON><PERSON>", "estimatedTime": "30-45 minutes", "prerequisites": [], "status": "available"}, {"id": "story-hierarchy", "title": "Story Hierarchy & Breakdown", "description": "Learn to structure and decompose user stories effectively", "icon": "📋", "difficulty": "Intermediate", "estimatedTime": "45-60 minutes", "prerequisites": ["story-points"], "status": "available"}, {"id": "sprint-planning", "title": "Sprint Planning Mastery", "description": "Master the art of planning effective sprints with realistic commitments", "icon": "🏃‍♂️", "difficulty": "Intermediate", "estimatedTime": "60-75 minutes", "prerequisites": ["story-points", "story-hierarchy"], "status": "available"}, {"id": "definition-of-done", "title": "Definition of Done & Quality Gates", "description": "Establish clear quality standards and implement effective quality gates throughout development", "icon": "✅", "difficulty": "Intermediate", "estimatedTime": "45-60 minutes", "prerequisites": ["story-hierarchy", "sprint-planning"], "status": "available"}]}}